import { useState, useEffect, useRef } from "react";
import {
  Camera,
  X,
  Flashlight,
  Upload,
  Play,
  Pause,
  AlertCircle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import type { ScanResult, CameraDevice } from "../services/qrScannerService";
import { QRScannerService } from "../services/qrScannerService";

interface QRScannerProps {
  onScan: (result: ScanResult) => void;
  onClose?: () => void;
  isActive?: boolean;
  className?: string;
}

const QRScanner = ({
  onScan,
  onClose,
  isActive = true,
  className = "",
}: QRScannerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string>("");
  const [cameras, setCameras] = useState<CameraDevice[]>([]);
  const [selectedCamera, setSelectedCamera] = useState<string>("");
  const [hasTorch, setHasTorch] = useState(false);
  const [torchEnabled, setTorchEnabled] = useState(false);
  const [lastScanResult, setLastScanResult] = useState<ScanResult | null>(null);

  useEffect(() => {
    if (isActive && videoRef.current) {
      initializeScanner();
    }

    return () => {
      cleanup();
    };
  }, [isActive]);

  const initializeScanner = async () => {
    if (!videoRef.current) return;

    try {
      setError("");

      // Request camera permission first
      const hasPermission = await QRScannerService.requestCameraPermission();
      if (!hasPermission) {
        throw new Error(
          "Camera permission denied. Please allow camera access to scan QR codes."
        );
      }

      // Get available cameras
      const availableCameras = await QRScannerService.getAvailableCameras();
      setCameras(availableCameras);

      // Use back camera if available, otherwise use first available
      const preferredCamera =
        availableCameras.find(
          (cam) =>
            cam.label.toLowerCase().includes("back") ||
            cam.label.toLowerCase().includes("environment")
        )?.id || availableCameras[0]?.id;

      setSelectedCamera(preferredCamera || "");

      // Initialize scanner
      await QRScannerService.initializeScanner(
        videoRef.current,
        handleScanResult,
        preferredCamera
      );

      // Check torch availability
      const capabilities = await QRScannerService.getCapabilities();
      setHasTorch(capabilities.hasTorch);

      setIsInitialized(true);
      setIsScanning(true);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to initialize camera";
      // setError(errorMessage);
      console.error("Scanner initialization error:", errorMessage);
    }
  };

  const handleScanResult = (result: ScanResult) => {
    setLastScanResult(result);
    onScan(result);

    // Briefly pause scanning on successful scan to prevent multiple rapid scans
    if (result.success) {
      pauseScanning();
      setTimeout(() => {
        resumeScanning();
      }, 2000);
    }
  };

  const cleanup = () => {
    QRScannerService.stopScanner();
    setIsInitialized(false);
    setIsScanning(false);
  };

  const pauseScanning = () => {
    QRScannerService.pauseScanner();
    setIsScanning(false);
  };

  const resumeScanning = () => {
    QRScannerService.resumeScanner();
    setIsScanning(true);
  };

  const switchCamera = async (cameraId: string) => {
    try {
      await QRScannerService.switchCamera(cameraId);
      setSelectedCamera(cameraId);
    } catch (err) {
      console.error("Failed to switch camera:", err);
    }
  };

  const toggleTorch = async () => {
    const success = await QRScannerService.toggleTorch();
    if (success) {
      setTorchEnabled(!torchEnabled);
    }
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await QRScannerService.scanFromFile(file);
      handleScanResult(result);
    } catch (err) {
      console.error("File scan error:", err);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const getScanResultIcon = (result: ScanResult) => {
    if (result.success) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  return (
    <div
      className={`relative bg-black rounded-lg overflow-hidden ${className}`}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        playsInline
        muted
      />

      {/* Overlay UI */}
      <div className="absolute inset-0 flex flex-col">
        {/* Top Controls */}
        <div className="flex justify-between items-center p-4 bg-gradient-to-b from-black/50 to-transparent">
          <div className="flex items-center space-x-2">
            <Camera className="h-6 w-6 text-white" />
            <span className="text-white font-medium">QR Scanner</span>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 rounded-full bg-black/30 text-white hover:bg-black/50"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Center Scanning Area */}
        <div className="flex-1 flex items-center justify-center p-4 sm:p-8">
          <div className="relative">
            {/* Scanning Frame */}
            {/* <div className="w-48 h-48 sm:w-64 sm:h-64 lg:w-80 lg:h-80 border-2 border-white/50 rounded-lg absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
              <div className="absolute top-0 left-0 w-6 h-6 sm:w-8 sm:h-8 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-6 h-6 sm:w-8 sm:h-8 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-6 h-6 sm:w-8 sm:h-8 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 sm:w-8 sm:h-8 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div> */}

            {/* Scanning Line Animation */}
            {/* {isScanning && (
                <div className="absolute inset-0 overflow-hidden rounded-lg">
                  <div className="w-full h-0.5 bg-blue-500 animate-pulse absolute top-1/2 transform -translate-y-1/2"></div>
                </div>
              )} */}
            {/* </div> */}

            {/* Status Text */}
            <div className="text-center mt-3 sm:mt-4 px-2">
              {error ? (
                <div className="flex items-center justify-center text-red-400">
                  <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" />
                  <span className="text-xs sm:text-sm">{error}</span>
                </div>
              ) : isScanning ? (
                <p className="text-white text-xs sm:text-sm">
                  Position QR code within the frame
                </p>
              ) : (
                <p className="text-white/70 text-xs sm:text-sm">
                  Scanner paused
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Controls */}
        <div className="p-3 sm:p-4 bg-gradient-to-t from-black/50 to-transparent">
          <div className="flex justify-center items-center space-x-2 sm:space-x-4">
            {/* Camera Switch */}
            {cameras.length > 1 && (
              <select
                value={selectedCamera}
                onChange={(e) => switchCamera(e.target.value)}
                className="px-2 py-1 sm:px-3 sm:py-2 bg-black/30 text-white rounded-lg border border-white/20 text-xs sm:text-sm max-w-32 sm:max-w-none"
              >
                {cameras.map((camera) => (
                  <option
                    key={camera.id}
                    value={camera.id}
                    className="bg-black text-white"
                  >
                    {camera.label}
                  </option>
                ))}
              </select>
            )}

            {/* Torch Toggle */}
            {hasTorch && (
              <button
                onClick={toggleTorch}
                className={`p-2 sm:p-3 rounded-full ${
                  torchEnabled ? "bg-yellow-500" : "bg-black/30"
                } text-white hover:bg-opacity-80 touch-manipulation`}
                title={
                  torchEnabled ? "Turn off flashlight" : "Turn on flashlight"
                }
              >
                <Flashlight className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            )}

            {/* Pause/Resume */}
            <button
              onClick={isScanning ? pauseScanning : resumeScanning}
              className="p-2 sm:p-3 rounded-full bg-blue-600 text-white hover:bg-blue-700 touch-manipulation"
              title={isScanning ? "Pause scanner" : "Resume scanner"}
            >
              {isScanning ? (
                <Pause className="h-4 w-4 sm:h-5 sm:w-5" />
              ) : (
                <Play className="h-4 w-4 sm:h-5 sm:w-5" />
              )}
            </button>

            {/* File Upload */}
            <button
              onClick={() => fileInputRef.current?.click()}
              className="p-3 rounded-full bg-black/30 text-white hover:bg-black/50"
            >
              <Upload className="h-5 w-5" />
            </button>
          </div>

          {/* Last Scan Result */}
          {lastScanResult && (
            <div className="mt-4 p-3 bg-black/30 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getScanResultIcon(lastScanResult)}
                  <span className="text-white text-sm">
                    {lastScanResult.success ? "Scan successful" : "Scan failed"}
                  </span>
                </div>
                <span className="text-white/70 text-xs">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
              {lastScanResult.error && (
                <p className="text-red-400 text-xs mt-1">
                  {lastScanResult.error}
                </p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Loading Overlay */}
      {!isInitialized && !error && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-sm">Initializing camera...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default QRScanner;
