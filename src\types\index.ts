// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: "organizer" | "admin";
  createdAt: Date;
  updatedAt: Date;
}

// Event date status enum
export const EventDateStatus = {
  ONGOING: 1,
  UPCOMING: 2,
  COMPLETED: 3,
} as const;

export type EventDateStatus =
  (typeof EventDateStatus)[keyof typeof EventDateStatus];

// Event types
// Base Event type based on SQL schema
export interface Event {
  id: string;
  name_vi: string;
  name_en: string;
  poster_vi: string;
  poster_en: string;
  event_group_id: number;
  ticket_price_desc_vi?: string;
  ticket_price_desc_en?: string;
  active: boolean;
  updated_at: string;
  created_at: string;
  deleted: boolean;
  showdate_from: number;
  showdate_to: number;
  venue_vi?: string;
  venue_en?: string;
  public_sale: number;
  meta_image_vi?: string;
  meta_image_en?: string;
  meta_title_vi?: string;
  meta_title_en?: string;
  meta_description_vi?: string;
  meta_description_en?: string;
  slug_vi?: string;
  slug_en?: string;
  uid?: string;
  content_vi?: string;
  content_en?: string;
  payment_gateways: string;
  code?: string;
  is_demo: boolean;
  is_hot: number;
  is_coming_soon: number;
  event_type: number;
  is_show_detail: boolean;
  banner_desktop_vi?: string;
  banner_desktop_en?: string;
  banner_mobile_vi?: string;
  banner_mobile_en?: string;
  email_poster_vi?: string;
  email_poster_en?: string;
  state_id: number;
  is_free_ticket: boolean;
  public_sale_to: number;
  type_highlight_note: number;
  content_highlight_vi?: string;
  content_highlight_en?: string;
  is_display: boolean;
  is_banner: boolean;
  is_unlimited: boolean;
  desc_unlimited_vi?: string;
  desc_unlimited_en?: string;
  attributes: {
    expect_text: string;
  };
  order_by: number;
  order_by_inner: number;

  // Computed properties for compatibility with components
  name?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
  location?: string;
  maxAttendees?: number;
  status?: number;
}

// Event list item type (for API responses that include nested data)
export interface EventListItem {
  id: string;
  event_stage_id: number;
  event_id: string;
  show_date: number;
  show_from: number;
  show_to: number;
  public_sale: number;
  public_sale_to: number;
  short_desc_vi: string;
  short_desc_en: string;
  ticket_price_desc_vi: string;
  ticket_price_desc_en: string;
  zone_map: string;
  zone_price_image_vi: string;
  zone_price_image_en: string;
  service_fee: string;
  active: boolean;
  is_free_ticket: boolean;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  allow_check_in: number;
  status: number;
  show_stage: number;
  salepoint_code: string | null;
  seat_type: number;
  ticket_type: string;
  email_note_vi: string;
  email_note_en: string;
  attributes: {
    width: string;
    height: string;
  };
  min_order: number;
  max_order: number;
  countdown: number;
  is_unlimited: boolean;
  email_poster_vi: string | null;
  email_poster_en: string | null;
  notify_email: string | null;
  content_vi: string | null;
  content_en: string | null;
  event_stage: {
    id: string;
    event_id: string;
    poster_stage_vi: string;
    poster_stage_en: string;
    stage_type: number;
    venue_vi: string;
    ticket_price_desc_vi: string;
    ticket_price_desc_en: string;
    venue_en: string;
    active: boolean;
    deleted: boolean;
    created_at: string;
    updated_at: string;
    event: Event;
  };
  total_checked_in: number;
  total_ticket: number;
}

// Attendee types
export interface Attendee {
  id: string;
  eventId: string;
  email: string;
  name: string;
  phone?: string;
  qrCode: string;
  registeredAt: Date;
  checkedIn: boolean;
  checkedInAt?: Date;
}

// Check-in types
export interface CheckIn {
  id: string;
  attendeeId: string;
  eventId: string;
  checkedInAt: Date;
  checkedInBy: string; // User ID who performed the check-in
}

// QR Code data structure
export interface QRCodeData {
  attendeeId: string;
  eventId: string;
  timestamp: number;
  signature: string; // For security/validation
}

// QR NFT check response
export interface QRCheckResponse {
  valid: boolean;
  message?: string;
  attendee?: {
    id: string;
    name: string;
    email: string;
  };
  event?: {
    id: string;
    name: string;
  };
}

// API Response types
export interface ApiResponse<T> {
  status: number;
  message: string;
  data?: T;
}

// Form types
export interface AttendeeFormData {
  name: string;
  email: string;
}

// Statistics types
export interface EventStats {
  totalAttendees: number;
  checkedInCount: number;
  checkInRate: number;
  hourlyCheckIns: { hour: string; count: number }[];
}

// Auth types
export interface AuthUser {
  id: string;
  phone: string;
  phone_prefix: string;
  email: string;
  status: number;
  name: string;
  lastname: string;
  role: "organizer" | "admin";
  token: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: "organizer" | "admin";
}
